#!/usr/bin/env node

// Test script to check what squad name is being fetched for trip ID: xKWk039GpqIHu3iQDWJI
// This replicates the logic from the activate-trips cron job

import { config } from "dotenv"
config({ path: ".env.vercel" })

async function testSquadFetch() {
  console.log('🔍 Testing squad name fetch - checking recent trips for "Unknown Squad" issue')
  console.log("=======================================================================")

  try {
    // Initialize Firebase Admin (same logic as in lib/firebase-admin.ts)
    const { initializeApp, cert, getApps } = await import("firebase-admin/app")
    const { getFirestore } = await import("firebase-admin/firestore")

    // Initialize Firebase Admin SDK if it hasn't been initialized
    if (!getApps().length) {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

      const options = {
        credential: cert(serviceAccount),
      }

      if (process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID) {
        options.databaseURL = `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`
      }

      initializeApp(options)
      console.log("✅ Firebase Admin initialized successfully")
    }

    const adminDb = getFirestore()

    // Test the specific trip ID first
    console.log("\n🎯 Testing specific trip ID: xKWk039GpqIHu3iQDWJI")
    await testSingleTrip(adminDb, "xKWk039GpqIHu3iQDWJI")

    // Then check recent trips that might be causing the issue
    console.log("\n📋 Checking recent trips that could be activated...")
    const tripsRef = adminDb.collection("trips")
    const recentTripsQuery = tripsRef.where("status", "in", ["planning", "active"]).limit(10)
    const recentTripsSnapshot = await recentTripsQuery.get()

    console.log(`Found ${recentTripsSnapshot.size} recent planning/active trips`)

    for (const tripDoc of recentTripsSnapshot.docs) {
      await testSingleTrip(adminDb, tripDoc.id)
    }
  } catch (error) {
    console.error("❌ Error running test:", error)
  }
}

// Helper function to test a single trip (replicates the cron job logic exactly)
async function testSingleTrip(adminDb, tripId) {
  console.log(`\n🔍 Testing Trip ID: ${tripId}`)
  console.log("----------------------------------------")

  try {
    // Step 1: Get the trip document
    const tripDoc = await adminDb.collection("trips").doc(tripId).get()

    if (!tripDoc.exists) {
      console.log("❌ Trip document does not exist!")
      return
    }

    const tripData = tripDoc.data()
    console.log("✅ Trip document found")
    console.log("Trip Name:", tripData.name || "Unknown Trip")
    console.log("Trip Destination:", tripData.destination || "Unknown Destination")
    console.log("Trip Status:", tripData.status || "Unknown Status")
    console.log("Squad ID:", tripData.squadId || "❌ NO SQUAD ID")

    // Step 2: EXACT REPLICA of cron job squad fetching logic
    console.log("\n🏷️  Testing squad fetching (EXACT cron job logic)...")

    // This is the EXACT code from the cron job (lines 194-203)
    let squadName = "Unknown Squad"
    try {
      console.log(`🔍 Calling: adminDb.collection("squads").doc("${tripData.squadId}").get()`)
      const squadDoc = await adminDb.collection("squads").doc(tripData.squadId).get()
      console.log(`📄 squadDoc.exists(): ${squadDoc.exists()}`)

      if (squadDoc.exists()) {
        const squadData = squadDoc.data()
        console.log(`📊 squadDoc.data():`, squadData)
        squadName = squadData?.name || "Unknown Squad"
        console.log(`✅ Final squadName: "${squadName}"`)
      } else {
        console.log("❌ Squad document does not exist - squadName remains 'Unknown Squad'")
      }
    } catch (squadError) {
      console.log("❌ Exception caught in squad fetching:", squadError.message)
      console.log("❌ squadName remains 'Unknown Squad' due to exception")
      // This is the problem! The cron job silently continues with "Unknown Squad"
    }

    // Step 3: Check if there are any attendees for this trip
    console.log("\n👥 Checking trip attendees...")
    const userTripsRef = adminDb.collection("userTrips")
    const userTripsQuery = userTripsRef.where("tripId", "==", tripId).where("status", "==", "going")
    const userTripsSnapshot = await userTripsQuery.get()

    console.log(`Found ${userTripsSnapshot.size} attendees with status "going"`)

    if (userTripsSnapshot.size > 0) {
      const attendeeIds = userTripsSnapshot.docs.map((doc) => doc.data().userId)
      console.log("Attendee IDs:", attendeeIds)

      // Get user details
      console.log("\n📧 Checking user details for email sending...")
      const userDocs = await Promise.all(
        attendeeIds.map((userId) => adminDb.collection("users").doc(userId).get())
      )

      const users = userDocs
        .filter((doc) => doc.exists)
        .map((doc) => ({ id: doc.id, ...doc.data() }))

      console.log(
        `Found ${users.length} valid user documents out of ${attendeeIds.length} attendees`
      )
      users.forEach((user) => {
        console.log(
          `- User: ${user.email || "No email"} (${user.firstName || "No name"} ${user.lastName || ""})`
        )
      })
    }

    // Summary for this trip
    console.log(`\n📋 RESULT: Trip "${tripData.name || "Unknown"}" → Squad Name: "${squadName}"`)

    if (squadName === "Unknown Squad") {
      console.log('⚠️  THIS TRIP WOULD SEND EMAILS WITH "Unknown Squad"!')
      if (!tripData.squadId) {
        console.log("   🔍 Root Cause: No squadId field in trip document")
      } else {
        console.log("   🔍 Root Cause: Squad document missing or error in fetching")
      }
    } else {
      console.log("✅ This trip would send emails with correct squad name")
    }
  } catch (error) {
    console.error("❌ Error running test:", error)
  }
}

// Run the test
testSquadFetch()
  .then(() => {
    console.log("\n🏁 Test completed")
    process.exit(0)
  })
  .catch((error) => {
    console.error("💥 Test failed:", error)
    process.exit(1)
  })
