#!/usr/bin/env node

// Test script to check what squad name is being fetched for trip ID: xKWk039GpqIHu3iQDWJI
// This replicates the logic from the activate-trips cron job

import { config } from 'dotenv';
config({ path: '.env.vercel' });

async function testSquadFetch() {
  console.log('🔍 Testing squad name fetch for trip ID: xKWk039GpqIHu3iQDWJI');
  console.log('================================================');

  try {
    // Initialize Firebase Admin (same logic as in lib/firebase-admin.ts)
    const { initializeApp, cert, getApps } = await import('firebase-admin/app');
    const { getFirestore } = await import('firebase-admin/firestore');

    // Initialize Firebase Admin SDK if it hasn't been initialized
    if (!getApps().length) {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error('FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set');
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
      
      const options = {
        credential: cert(serviceAccount),
      };

      if (process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID) {
        options.databaseURL = `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`;
      }

      initializeApp(options);
      console.log('✅ Firebase Admin initialized successfully');
    }

    const adminDb = getFirestore();
    const tripId = 'xKWk039GpqIHu3iQDWJI';

    // Step 1: Get the trip document
    console.log('\n📄 Fetching trip document...');
    const tripDoc = await adminDb.collection('trips').doc(tripId).get();
    
    if (!tripDoc.exists) {
      console.log('❌ Trip document does not exist!');
      return;
    }

    const tripData = tripDoc.data();
    console.log('✅ Trip document found');
    console.log('Trip Name:', tripData.name || 'Unknown Trip');
    console.log('Trip Destination:', tripData.destination || 'Unknown Destination');
    console.log('Trip Status:', tripData.status || 'Unknown Status');
    console.log('Squad ID:', tripData.squadId || 'No Squad ID');

    // Step 2: Get the squad information (this is the part that might be failing)
    console.log('\n🏷️  Fetching squad information...');
    
    if (!tripData.squadId) {
      console.log('❌ No squadId found in trip document!');
      return;
    }

    let squadName = 'Unknown Squad';
    try {
      console.log(`Attempting to fetch squad document: ${tripData.squadId}`);
      const squadDoc = await adminDb.collection('squads').doc(tripData.squadId).get();
      
      if (squadDoc.exists) {
        const squadData = squadDoc.data();
        squadName = squadData?.name || 'Unknown Squad';
        console.log('✅ Squad document found');
        console.log('Squad Name:', squadName);
        console.log('Squad Data:', JSON.stringify(squadData, null, 2));
      } else {
        console.log('❌ Squad document does not exist!');
        console.log(`Squad ID "${tripData.squadId}" was not found in the squads collection`);
      }
    } catch (squadError) {
      console.log('❌ Error fetching squad document:', squadError.message);
    }

    // Step 3: Check if there are any attendees for this trip
    console.log('\n👥 Checking trip attendees...');
    const userTripsRef = adminDb.collection('userTrips');
    const userTripsQuery = userTripsRef
      .where('tripId', '==', tripId)
      .where('status', '==', 'going');
    const userTripsSnapshot = await userTripsQuery.get();

    console.log(`Found ${userTripsSnapshot.size} attendees with status "going"`);
    
    if (userTripsSnapshot.size > 0) {
      const attendeeIds = userTripsSnapshot.docs.map(doc => doc.data().userId);
      console.log('Attendee IDs:', attendeeIds);
      
      // Get user details
      console.log('\n📧 Checking user details for email sending...');
      const userDocs = await Promise.all(
        attendeeIds.map(userId => adminDb.collection('users').doc(userId).get())
      );

      const users = userDocs
        .filter(doc => doc.exists)
        .map(doc => ({ id: doc.id, ...doc.data() }));

      console.log(`Found ${users.length} valid user documents out of ${attendeeIds.length} attendees`);
      users.forEach(user => {
        console.log(`- User: ${user.email || 'No email'} (${user.firstName || 'No name'} ${user.lastName || ''})`);
      });
    }

    // Summary
    console.log('\n📋 SUMMARY');
    console.log('==========');
    console.log(`Trip ID: ${tripId}`);
    console.log(`Trip Name: ${tripData.name || 'Unknown Trip'}`);
    console.log(`Squad ID: ${tripData.squadId || 'No Squad ID'}`);
    console.log(`Squad Name: ${squadName}`);
    console.log(`Attendees: ${userTripsSnapshot.size}`);
    
    if (squadName === 'Unknown Squad') {
      console.log('\n⚠️  WARNING: Squad name is "Unknown Squad" - this is why emails show "Unknown Squad"');
      if (!tripData.squadId) {
        console.log('   Reason: No squadId field in trip document');
      } else {
        console.log('   Reason: Squad document does not exist or has no name field');
      }
    } else {
      console.log('\n✅ Squad name should appear correctly in emails');
    }

  } catch (error) {
    console.error('❌ Error running test:', error);
  }
}

// Run the test
testSquadFetch().then(() => {
  console.log('\n🏁 Test completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test failed:', error);
  process.exit(1);
});
